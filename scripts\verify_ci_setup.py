#!/usr/bin/env python3
"""
🔍 CI配置验证脚本
验证所有CI工具是否正确安装和配置
"""

from pathlib import Path
import subprocess
import sys


def run_command(cmd: list[str], description: str) -> tuple[bool, str]:
    """运行命令并返回结果"""
    try:
        result = subprocess.run(
            cmd, capture_output=True, text=True, check=True, timeout=60
        )
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        return False, f"Error: {e.stderr}"
    except subprocess.TimeoutExpired:
        return False, "Timeout: Command took too long"
    except FileNotFoundError:
        return False, f"Command not found: {cmd[0]}"


def check_tool_installation():
    """检查工具安装状态"""
    print("🔍 检查CI工具安装状态...")
    print("=" * 50)

    tools = [
        (["ruff", "--version"], "Ruff"),
        (["mypy", "--version"], "MyPy"),
        (["pytest", "--version"], "Pytest"),
        (["pip-audit", "--version"], "pip-audit"),
        (["semgrep", "--version"], "Semgrep"),
        (["docker", "--version"], "Docker (for TruffleHog)"),
        (["pre-commit", "--version"], "pre-commit"),
    ]

    all_installed = True
    for cmd, name in tools:
        success, output = run_command(cmd, f"Check {name}")
        if success:
            version = output.strip().split("\n")[0]
            print(f"✅ {name}: {version}")
        else:
            print(f"❌ {name}: {output}")
            all_installed = False

    return all_installed


def check_configuration_files():
    """检查配置文件"""
    print("\n🔧 检查配置文件...")
    print("=" * 50)

    required_files = [
        "pyproject.toml",
        ".pre-commit-config.yaml",
        ".gitattributes",
        ".github/workflows/ci.yml",
        # 注意：不再需要 .bandit 和 .secrets.baseline（已被现代化工具替代）
    ]

    all_present = True
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - 文件不存在")
            all_present = False

    return all_present


def run_quick_checks():
    """运行快速检查"""
    print("\n🧪 运行快速检查...")
    print("=" * 50)

    checks = [
        (["ruff", "check", ".", "--select", "E9,F63,F7,F82"], "Ruff语法检查"),
        (["mypy", "--config-file", "pyproject.toml", "app"], "MyPy类型检查"),
        (["pip-audit", "--desc", "--disable-pip", "--dry-run"], "依赖安全检查"),
        # TruffleHog通过Docker运行，在CI中使用官方GitHub Action
    ]

    all_passed = True
    for cmd, description in checks:
        print(f"\n🔍 {description}...")
        success, output = run_command(cmd, description)
        if success:
            print(f"✅ {description}: 通过")
        else:
            print(f"⚠️ {description}: {output[:200]}...")
            # 注意：某些检查可能会有警告，但不一定是错误

    return all_passed


def check_semgrep():
    """单独检查Semgrep（可能需要网络）"""
    print("\n🛡️ 检查Semgrep...")
    print("=" * 50)

    # 先检查基本功能
    success, output = run_command(
        ["semgrep", "--config=p/python", "--dry-run", "app/"], "Semgrep基础检查"
    )

    if success:
        print("✅ Semgrep: 配置正确，可以正常运行")
        return True
    else:
        print(f"⚠️ Semgrep: {output}")
        print("💡 提示: Semgrep首次运行可能需要下载规则，请确保网络连接正常")
        return False


def main():
    """主函数"""
    print("🚀 CI配置验证工具")
    print("=" * 50)

    results = []

    # 检查工具安装
    results.append(("工具安装", check_tool_installation()))

    # 检查配置文件
    results.append(("配置文件", check_configuration_files()))

    # 运行快速检查
    results.append(("快速检查", run_quick_checks()))

    # 检查Semgrep
    results.append(("Semgrep检查", check_semgrep()))

    # 总结
    print("\n" + "=" * 50)
    print("📊 验证结果总结")
    print("=" * 50)

    all_passed = True
    for name, passed in results:
        status = "✅ 通过" if passed else "❌ 需要修复"
        print(f"{name}: {status}")
        all_passed &= passed

    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有检查都通过了！CI配置已就绪。")
        print("\n📝 下一步:")
        print("1. 运行 'pre-commit install' 安装Git钩子")
        print("2. 提交代码测试CI流程")
        print("3. 查看GitHub Actions运行结果")
        return 0
    else:
        print("⚠️ 部分检查失败，请修复后重新运行")
        print("\n🔧 修复建议:")
        print("1. 确保所有依赖已安装: pip install -e '.[development]'")
        print("2. 检查配置文件是否存在且格式正确")
        print("3. 确保网络连接正常（Semgrep需要下载规则）")
        return 1


if __name__ == "__main__":
    sys.exit(main())
