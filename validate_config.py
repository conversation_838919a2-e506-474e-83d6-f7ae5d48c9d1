#!/usr/bin/env python3
"""
配置文件验证脚本
验证项目配置文件的正确性和最佳实践符合度
"""

from pathlib import Path
import sys

try:
    import tomllib
except ImportError:
    try:
        import tomli as tomllib
    except ImportError:
        print("❌ 需要安装tomli库: pip install tomli")
        sys.exit(1)

import yaml


def validate_pyproject_toml() -> bool:
    """验证 pyproject.toml 配置"""
    print("🔍 验证 pyproject.toml...")

    try:
        with open("pyproject.toml", "rb") as f:
            config = tomllib.load(f)

        # 检查必要的部分
        required_sections = [
            "build-system",
            "project",
            "project.dependencies",
            "project.optional-dependencies",
            "tool.ruff",
            "tool.pytest.ini_options",
        ]

        issues = []
        for section in required_sections:
            keys = section.split(".")
            current = config
            try:
                for key in keys:
                    current = current[key]
                print(f"  ✅ {section}")
            except KeyError:
                issues.append(f"  ❌ 缺少配置节: {section}")

        # 检查依赖组命名
        optional_deps = config.get("project", {}).get("optional-dependencies", {})
        recommended_groups = ["development", "testing", "production"]
        deprecated_groups = ["dev", "test", "prod"]

        group_rename_map = {
            "dev": "development",
            "test": "testing",
            "prod": "production",
        }
        for group in optional_deps:
            if group in deprecated_groups:
                recommended = group_rename_map.get(group, "")
                issues.append(f"  ⚠️ 建议重命名依赖组: {group} -> {recommended}")
            elif group in recommended_groups:
                print(f"  ✅ 依赖组命名规范: {group}")

        # 检查工具配置
        tools = {
            "ruff": "tool.ruff",
            "mypy": "tool.mypy",
            "pytest": "tool.pytest.ini_options",
            "coverage": "tool.coverage.run",
        }

        for tool, path in tools.items():
            keys = path.split(".")
            current = config
            try:
                for key in keys:
                    current = current[key]
                print(f"  ✅ {tool} 配置存在")
            except KeyError:
                issues.append(f"  ⚠️ 建议添加 {tool} 配置")

        if issues:
            print("\n".join(issues))
            return False
        else:
            print("  🎉 pyproject.toml 配置完美！")
            return True

    except Exception as e:
        print(f"  ❌ pyproject.toml 解析失败: {e}")
        return False


def validate_precommit_config() -> bool:
    """验证 pre-commit 配置"""
    print("\n🔍 验证 .pre-commit-config.yaml...")

    try:
        with open(".pre-commit-config.yaml", encoding="utf-8") as f:
            config = yaml.safe_load(f)

        # 检查必要的钩子（现代化工具链）
        required_hooks = ["ruff", "ruff-format", "trufflehog", "semgrep", "pip-audit"]
        found_hooks = []

        for repo in config.get("repos", []):
            for hook in repo.get("hooks", []):
                hook_id = hook.get("id", "")
                if hook_id in required_hooks:
                    found_hooks.append(hook_id)
                    print(f"  ✅ {hook_id} 钩子配置")

        missing_hooks = set(required_hooks) - set(found_hooks)
        if missing_hooks:
            for hook in missing_hooks:
                print(f"  ⚠️ 建议添加钩子: {hook}")
            return False

        # 检查版本固定
        version_issues = []
        for repo in config.get("repos", []):
            rev = repo.get("rev", "")
            if not rev or rev == "main" or rev == "master":
                version_issues.append(
                    f"  ⚠️ 建议固定版本: {repo.get('repo', 'unknown')}"
                )

        if version_issues:
            print("\n".join(version_issues))
            return False

        print("  🎉 pre-commit 配置良好！")
        return True

    except Exception as e:
        print(f"  ❌ .pre-commit-config.yaml 解析失败: {e}")
        return False


# 注意：已移除 validate_secrets_baseline 函数
# 原因：项目已迁移到 TruffleHog，不再使用 detect-secrets


def check_file_conflicts() -> bool:
    """检查配置文件冲突"""
    print("\n🔍 检查配置文件冲突...")

    conflicts = []

    # 检查pytest配置冲突
    if Path("pytest.ini").exists():
        conflicts.append("  ⚠️ pytest.ini 与 pyproject.toml 中的 pytest 配置冲突")

    if Path("setup.py").exists():
        conflicts.append("  ⚠️ setup.py 与 pyproject.toml 冲突，建议删除 setup.py")

    if Path("requirements.txt").exists():
        conflicts.append("  ⚠️ requirements.txt 与 pyproject.toml 依赖管理冲突")

    # 检查格式化工具冲突
    if Path(".black").exists() or Path(".isort.cfg").exists():
        conflicts.append("  ⚠️ 发现其他格式化工具配置，建议统一使用 Ruff")

    if conflicts:
        print("\n".join(conflicts))
        return False
    else:
        print("  ✅ 无配置文件冲突")
        return True


def main():
    """主函数"""
    print("🔧 项目配置验证工具")
    print("=" * 50)

    results = []

    # 验证各个配置文件
    results.append(("pyproject.toml", validate_pyproject_toml()))
    results.append(("pre-commit配置", validate_precommit_config()))
    # 注意：已移除 secrets 配置验证，改用 TruffleHog
    results.append(("文件冲突检查", check_file_conflicts()))

    # 总结
    print("\n" + "=" * 50)
    print("📊 验证结果总结")
    print("=" * 50)

    all_passed = True
    for name, passed in results:
        status = "✅ 通过" if passed else "❌ 需要改进"
        print(f"{name}: {status}")
        all_passed &= passed

    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有配置都符合最佳实践！")
        print("💡 建议定期运行此脚本检查配置")
    else:
        print("⚠️ 部分配置需要改进")
        print("📖 请参考 docs/DEVELOPMENT_TOOLS_GUIDE.md")

    return 0 if all_passed else 1


if __name__ == "__main__":
    sys.exit(main())
