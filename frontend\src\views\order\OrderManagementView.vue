<template>
  <div class="order-management">
    <!-- 订单查询卡片 - 合并标题和筛选 -->
    <el-card class="order-query-section" shadow="hover">
      <template #header>
        <div class="section-header">
          <h2>我的订单</h2>
          <div class="polling-status">
            <el-tag
              v-if="isPolling"
              type="success"
              size="small"
              :icon="Refresh"
              class="polling-indicator"
            >
              实时更新中
            </el-tag>
            <el-tag v-else-if="!isConnected" type="warning" size="small"> 连接异常 </el-tag>
          </div>
        </div>
      </template>
      <el-form ref="filterFormRef" :model="filterForm" inline label-width="80px">
        <el-row :gutter="16">
          <el-col :xs="24" :sm="12" :md="6" :lg="6">
            <el-form-item label="订单编号">
              <el-input
                v-model="filterForm.order_no"
                placeholder="输入订单编号"
                clearable
                style="width: 200px"
              />
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="6" :lg="6">
            <el-form-item label="申请编号">
              <el-input
                v-model="filterForm.application_number"
                placeholder="输入越南官方编号"
                clearable
                style="width: 200px"
              />
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="6" :lg="6">
            <el-form-item label="订单状态">
              <el-select
                v-model="filterForm.status"
                placeholder="选择状态"
                clearable
                style="width: 200px"
                @change="handleStatusChange"
              >
                <el-option
                  v-for="status in statusOptions"
                  :key="status.value"
                  :label="status.label"
                  :value="status.value"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :xs="24" :sm="12" :md="6" :lg="6">
            <el-form-item label="创建时间">
              <el-date-picker
                v-model="dateRange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                clearable
                style="width: 260px"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="24">
            <div class="filter-actions">
              <el-button type="primary" @click="handleSearch" :loading="loading"> 查询 </el-button>
              <el-button @click="handleReset">重置</el-button>
              <el-button type="success" @click="handleRefresh" :icon="Refresh" :loading="loading">
                刷新
              </el-button>
              <el-button
                type="primary"
                @click="handleExport"
                :icon="Download"
                :loading="exportLoading"
              >
                导出订单表
              </el-button>
              <el-button
                v-if="hasError && canRetry"
                type="warning"
                @click="handleRetry"
                :icon="Connection"
              >
                重连
              </el-button>
            </div>
          </el-col>
        </el-row>

        <!-- 错误提示 -->
        <el-row v-if="hasError">
          <el-col :span="24">
            <el-alert
              :title="errorMessage"
              type="warning"
              :closable="true"
              show-icon
              class="error-alert"
              @close="clearError"
            >
              <template v-if="canRetry" #default>
                {{ errorMessage }}
                <el-button type="text" size="small" @click="handleRetry" style="margin-left: 8px">
                  立即重试
                </el-button>
              </template>
            </el-alert>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 统计信息 -->
    <el-row :gutter="16" class="stats-section">
      <el-col :xs="12" :sm="6" :md="6" :lg="6">
        <el-statistic title="总订单数" :value="stats.total" />
      </el-col>
      <el-col :xs="12" :sm="6" :md="6" :lg="6">
        <el-statistic title="提交中" :value="stats.processing" />
      </el-col>
      <el-col :xs="12" :sm="6" :md="6" :lg="6">
        <el-statistic title="提交成功" :value="stats.completed" />
      </el-col>
      <el-col :xs="12" :sm="6" :md="6" :lg="6">
        <el-statistic title="提交失败" :value="stats.failed" />
      </el-col>
    </el-row>

    <!-- 订单列表 -->
    <el-card class="order-list" shadow="never">
      <template #header>
        <div class="card-header">
          <h3>订单列表</h3>
          <div class="header-actions">
            <el-tag v-if="recentUpdates.length > 0" type="success" size="small">
              最近更新: {{ recentUpdates.length }}
            </el-tag>
          </div>
        </div>
      </template>

      <!-- 加载状态 -->
      <div v-if="loading" class="loading-section">
        <el-skeleton :rows="3" animated />
        <div class="loading-text">正在加载订单数据...</div>
      </div>

      <!-- 空状态 -->
      <div v-else-if="displayOrders.length === 0" class="empty-section">
        <el-empty description="暂无订单数据">
          <template #image>
            <el-icon size="64" color="#909399">
              <Document />
            </el-icon>
          </template>
          <el-button type="primary" @click="router.push('/visa-form')"> 立即申请签证 </el-button>
        </el-empty>
      </div>

      <!-- 订单表格列表 -->
      <div v-else class="order-table">
        <el-table
          :data="displayOrders"
          stripe
          size="default"
          table-layout="auto"
          class="compact-table"
        >
          <!-- 订单编号列 -->
          <el-table-column prop="order_no" label="订单编号" width="180" fixed="left">
            <template #default="{ row }">
              <div class="order-no-cell">
                <span class="order-number">{{ formatOrderDisplay(row.order_no) }}</span>
                <el-button
                  type="primary"
                  text
                  size="small"
                  @click="copyOrderNo(row.order_no)"
                  :icon="CopyDocument"
                  class="copy-btn"
                />
              </div>
            </template>
          </el-table-column>

          <!-- 申请人信息列 -->
          <el-table-column label="申请人信息" width="240">
            <template #default="{ row }">
              <div class="applicant-info">
                <div class="name">{{ row.applicant_name }}</div>
                <div class="details">
                  <span class="passport">{{ row.passport_number }}</span>
                  <span class="separator">|</span>
                  <span class="birthday">{{ row.date_of_birth }}</span>
                </div>
              </div>
            </template>
          </el-table-column>

          <!-- 状态列 -->
          <el-table-column prop="status" label="状态" width="120" align="center">
            <template #default="{ row }">
              <el-tag
                :type="getStatusType(row.status)"
                size="default"
                :class="{ 'status-updated': isRecentlyUpdated(row.order_no) }"
              >
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>

          <!-- 创建时间列 -->
          <el-table-column prop="created_at" label="创建时间" width="180" align="center">
            <template #default="{ row }">
              <div class="time-cell">
                {{ formatTime(row.created_at) }}
              </div>
            </template>
          </el-table-column>

          <!-- 操作列 -->
          <el-table-column label="操作" width="240" fixed="right" align="center">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button
                  type="primary"
                  size="small"
                  @click="handleViewDetail(row.order_no)"
                  :icon="View"
                >
                  详情
                </el-button>

                <el-button
                  v-if="canDownload(row.status)"
                  type="success"
                  size="small"
                  @click="handleDownload(row.order_no)"
                  :icon="Download"
                >
                  下载
                </el-button>

                <el-button
                  v-if="canCancel(row.status)"
                  type="danger"
                  size="small"
                  @click="handleCancel(row.order_no)"
                  :icon="Close"
                >
                  取消
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 分页 -->
      <div v-if="displayOrders.length > 0" class="pagination-section">
        <el-pagination
          v-model:current-page="pagination.current_page"
          v-model:page-size="pagination.page_size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total_items"
          background
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handlePageSizeChange"
          @current-change="handlePageChange"
        />
      </div>
    </el-card>

    <!-- 订单详情弹窗 -->
    <OrderDetailDialog
      :visible="detailDialogVisible"
      :order-no="selectedOrderNo"
      @update:visible="detailDialogVisible = $event"
      @download="handleDownload"
    />

    <!-- 状态更新通知 -->
    <NotificationToast
      v-for="notification in activeNotifications"
      :key="notification.id"
      :visible="true"
      :type="notification.type"
      :title="notification.title"
      :message="notification.message"
      :order-no="notification.orderNo"
      @close="removeNotification(notification.id)"
    />
  </div>
</template>

<script setup lang="ts">
import OrderAPI from '@/api/order'
import type { OrderInfo, OrderStatus, PaginationInfo } from '@/api/types'
import NotificationToast from '@/components/common/NotificationToast.vue'
import OrderDetailDialog from '@/components/order/OrderDetailDialog.vue'
import { useTaskPolling as useOrderPolling } from '@/composables/useTaskPolling'
import { OrderNumberGenerator, OrderStatusHelper } from '@/utils/orderNumber'
import {
  Close,
  Connection,
  CopyDocument,
  Document,
  Download,
  Refresh,
  View,
} from '@element-plus/icons-vue'
import type { FormInstance } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'
import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue'
import { useRouter } from 'vue-router'

// Router
const router = useRouter()

// 订单轮询 - 核心功能
const {
  isPolling,
  isConnected,
  orders: polledOrders,
  hasError,
  errorMessage,
  canRetry,
  clearError,
  statusUpdates,
  getRecentUpdates,
  startPolling,
  stopPolling,
  performPolling,
} = useOrderPolling({
  interval: 30000, // 30 seconds as specified
  enabled: true,
})

// 本地状态
const loading = ref(false)
const exportLoading = ref(false)
const manualOrders = ref<OrderInfo[]>([])
const actionLoading = ref<Record<string, boolean>>({})

// 筛选表单
const filterFormRef = ref<FormInstance>()
const filterForm = reactive({
  order_no: '',
  application_number: '',
  status: '' as string,
})

// 日期范围
const dateRange = ref<[string, string] | undefined>(undefined)

// 分页信息
const pagination = reactive<PaginationInfo>({
  current_page: 1,
  total_pages: 0,
  total_items: 0,
  has_prev: false,
  has_next: false,
  page_size: 20,
})

// 统计信息
const stats = reactive({
  total: 0,
  processing: 0,
  completed: 0,
  failed: 0,
})

// 详情弹窗相关
const detailDialogVisible = ref(false)
const selectedOrderNo = ref('')

// 通知管理
const activeNotifications = ref<
  Array<{
    id: string
    type: 'success' | 'error' | 'warning' | 'info'
    title: string
    message: string
    orderNo?: string
  }>
>([])

// 计算属性
const statusOptions = computed(() => [
  { label: '订单已创建', value: 'created' },
  { label: '提交成功', value: 'success' },
  { label: '提交失败', value: 'failed' },
  { label: '等待审批', value: 'pending_approve' },
  { label: '已审批', value: 'approved' },
  { label: '等待下载', value: 'pending_download' },
  { label: '已下载', value: 'downloaded' },
  { label: '已付款', value: 'paid' },
  { label: '状态未知', value: 'unknown' },
])

// 显示的订单列表（轮询数据优先，手动查询为补充）
const displayOrders = computed(() => {
  // 如果有轮询数据且没有应用筛选，优先使用轮询数据
  const hasFilters =
    filterForm.order_no || filterForm.application_number || filterForm.status || dateRange.value

  if (!hasFilters && polledOrders.value.length > 0) {
    return [...polledOrders.value] // 转换为可变数组
  }

  // 否则使用手动查询数据
  return [...manualOrders.value] // 转换为可变数组
})

// 最近状态更新
const recentUpdates = computed(() => getRecentUpdates(5))

// 检查订单是否最近更新过
const isRecentlyUpdated = (orderNo: string): boolean => {
  return recentUpdates.value.some((update) => update.orderNo === orderNo)
}

// 方法
const loadOrders = async (): Promise<void> => {
  try {
    loading.value = true

    const params = {
      page: pagination.current_page,
      limit: pagination.page_size,
      order_no: filterForm.order_no ?? undefined,
      application_number: filterForm.application_number ?? undefined,
      status: (filterForm.status as OrderStatus) ?? undefined,
      date_from: dateRange.value?.[0] ?? undefined,
      date_to: dateRange.value?.[1] ?? undefined,
    }

    const response = await OrderAPI.queryOrders(params)

    if (response.success && response.data) {
      manualOrders.value = response.data.orders || []

      // 更新分页信息
      if (response.data.pagination) {
        Object.assign(pagination, response.data.pagination)
      }

      // 加载统计信息
      await loadStats()
    } else {
      ElMessage.error(response.message ?? '查询订单失败')
      manualOrders.value = []
    }
  } catch (error: unknown) {
    console.error('加载订单失败:', error)

    const errorObj = error as { error?: string; message?: string }

    if (errorObj.error === 'Unauthorized') {
      ElMessage.error('登录已过期，请重新登录')
    } else {
      const errorMessage =
        errorObj.message ?? (error instanceof Error ? error.message : '查询订单失败')
      ElMessage.error(errorMessage)
    }
    manualOrders.value = []
  } finally {
    loading.value = false
  }
}

const loadStats = async (): Promise<void> => {
  try {
    const statsResponse = await OrderAPI.getOrderStats()
    if (statsResponse.success && statsResponse.data) {
      Object.assign(stats, statsResponse.data)
    }
  } catch (error: unknown) {
    console.error('加载统计信息失败:', error)
    // Convert readonly array to mutable array for stats calculation
    const orders = displayOrders.value ? [...displayOrders.value] : []
    updateStatsFromCurrentPage(orders)
  }
}

const updateStatsFromCurrentPage = (orderList: OrderInfo[]): void => {
  // Convert readonly array to mutable array for calculation
  const orders = Array.isArray(orderList) ? [...orderList] : []

  stats.total = pagination.total_items || orders.length
  stats.processing = orders.filter((order) =>
    ['created', 'success', 'pending_approve', 'approved', 'pending_download'].includes(
      order.status,
    ),
  ).length
  stats.completed = orders.filter((order) => ['downloaded', 'paid'].includes(order.status)).length
  stats.failed = orders.filter((order) => order.status === 'failed').length
}

const handleSearch = (): void => {
  pagination.current_page = 1

  // 如果有筛选条件，停止轮询，使用手动查询
  const hasFilters =
    filterForm.order_no || filterForm.application_number || filterForm.status || dateRange.value

  if (hasFilters) {
    stopPolling()
    loadOrders()
  } else {
    // 无筛选条件，重新启动轮询
    loadOrders()
    nextTick(() => {
      startPolling()
    })
  }
}

const handleReset = (): void => {
  filterFormRef.value?.resetFields()
  filterForm.order_no = ''
  filterForm.application_number = ''
  filterForm.status = '' as string
  dateRange.value = undefined
  pagination.current_page = 1

  // 重置后启动轮询
  loadOrders()
  nextTick(() => {
    startPolling()
  })
}

const handleRefresh = (): void => {
  const hasFilters =
    filterForm.order_no || filterForm.application_number || filterForm.status || dateRange.value

  if (hasFilters) {
    loadOrders()
  } else {
    // 手动触发轮询刷新
    performPolling()
  }
}

const handleExport = async (): Promise<void> => {
  try {
    exportLoading.value = true

    // 构建导出参数 (仅支持后端API支持的参数)
    const params = new URLSearchParams({
      format: 'excel',
      ...(filterForm.status && { status: filterForm.status }),
      ...(dateRange.value?.[0] && { date_from: dateRange.value[0] }),
      ...(dateRange.value?.[1] && { date_to: dateRange.value[1] }),
    })

    // 🔐 使用用户级导出API（安全）
    const token = localStorage.getItem('auth_token') ?? localStorage.getItem('access_token')
    const response = await fetch(`/api/visa/export?${params}`, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })

    if (response.ok) {
      const blob = await response.blob()
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url

      // 从响应头提取文件名
      let filename = `越南签证订单表_${new Date().toISOString().slice(0, 10)}.xlsx` // 默认文件名
      const contentDisposition = response.headers.get('Content-Disposition')
      if (contentDisposition) {
        // 解析 Content-Disposition 头中的文件名
        const filenameMatch = contentDisposition.match(/filename\*=UTF-8''(.+)/)
        if (filenameMatch) {
          filename = decodeURIComponent(filenameMatch[1])
        } else {
          // 尝试解析标准的 filename= 格式
          const standardMatch = contentDisposition.match(/filename="?([^"]+)"?/)
          if (standardMatch) {
            filename = standardMatch[1]
          }
        }
      }

      link.download = filename
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)

      ElMessage.success('导出成功')
    } else if (response.status === 401) {
      ElMessage.error('登录已过期，请重新登录')
      router.push('/login')
    } else {
      const errorText = await response.text()
      ElMessage.error(`导出失败: ${response.status} ${errorText}`)
    }
  } catch (error: unknown) {
    console.error('导出失败:', error)
    ElMessage.error('导出失败')
  } finally {
    exportLoading.value = false
  }
}

const handleRetry = (): void => {
  performPolling()
}

const handlePageChange = (page: number): void => {
  pagination.current_page = page
  loadOrders()
}

const handlePageSizeChange = (size: number): void => {
  pagination.page_size = size
  pagination.current_page = 1
  loadOrders()
}

const handleViewDetail = (orderNo: string): void => {
  selectedOrderNo.value = orderNo
  detailDialogVisible.value = true
}

const handleDownload = (orderNo: string): void => {
  console.log('下载签证:', orderNo)
  ElMessage.info('下载功能开发中...')
}

const handleCancel = async (orderNo: string): Promise<void> => {
  try {
    await ElMessageBox.confirm('确定要取消此订单吗？取消后无法恢复。', '确认取消', {
      confirmButtonText: '确定取消',
      cancelButtonText: '保留订单',
      type: 'warning',
    })

    actionLoading.value[orderNo] = true

    const response = await OrderAPI.cancelOrder(orderNo)

    if (response.success) {
      ElMessage.success('订单已取消')
      // 刷新数据
      handleRefresh()
    } else {
      ElMessage.error(response.message ?? '取消失败')
    }
  } catch (error: unknown) {
    if (error !== 'cancel') {
      const errorMessage = error instanceof Error ? error.message : '取消失败'
      ElMessage.error(errorMessage)
    }
  } finally {
    actionLoading.value[orderNo] = false
  }
}

const handleStatusChange = (value: string): void => {
  console.log('状态选择器变化:', value)
  handleSearch()
}

// 表格工具方法
const formatOrderDisplay = (orderNo: string): string => {
  return OrderNumberGenerator.formatDisplay(orderNo)
}

const copyOrderNo = async (orderNo: string): Promise<void> => {
  try {
    await navigator.clipboard.writeText(orderNo)
    ElMessage.success('订单编号已复制')
  } catch {
    ElMessage.error('复制失败')
  }
}

const getStatusType = (status: string): 'success' | 'primary' | 'warning' | 'info' | 'danger' => {
  const type = OrderStatusHelper.getStatusType(status as OrderStatus)
  // 确保返回值符合Element Plus Tag组件的类型要求
  if (['success', 'primary', 'warning', 'info', 'danger'].includes(type)) {
    return type as 'success' | 'primary' | 'warning' | 'info' | 'danger'
  }
  return 'info' // 默认值
}

const getStatusText = (status: string): string => {
  return OrderStatusHelper.getStatusText(status as OrderStatus)
}

const formatTime = (timeStr: string): string => {
  try {
    return new Date(timeStr).toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    })
  } catch {
    return timeStr
  }
}

const canDownload = (status: string): boolean => {
  return OrderStatusHelper.canDownload(status as OrderStatus)
}

const canCancel = (status: string): boolean => {
  return OrderStatusHelper.canCancel(status as OrderStatus)
}

// 通知管理
const showNotification = (
  type: 'success' | 'error' | 'warning' | 'info',
  title: string,
  message: string,
  orderNo?: string,
): void => {
  const notification = {
    id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
    type,
    title,
    message,
    orderNo,
  }

  activeNotifications.value.push(notification)

  // 限制同时显示的通知数量
  if (activeNotifications.value.length > 3) {
    activeNotifications.value = activeNotifications.value.slice(-3)
  }
}

const removeNotification = (id: string): void => {
  const index = activeNotifications.value.findIndex((n) => n.id === id)
  if (index > -1) {
    activeNotifications.value.splice(index, 1)
  }
}

// 监听状态更新并显示通知
watch(
  statusUpdates,
  (newUpdates) => {
    if (newUpdates.length > 0) {
      const latestUpdate = newUpdates[0]
      const statusText = getStatusText(latestUpdate.newStatus)

      let notificationType: 'success' | 'error' | 'warning' | 'info' = 'info'
      if (['downloaded', 'paid'].includes(latestUpdate.newStatus)) {
        notificationType = 'success'
      } else if (latestUpdate.newStatus === 'failed') {
        notificationType = 'error'
      } else if (['success', 'approved'].includes(latestUpdate.newStatus)) {
        notificationType = 'success'
      }

      showNotification(
        notificationType,
        '订单状态更新',
        `订单状态已更新为: ${statusText}`,
        latestUpdate.orderNo,
      )
    }
  },
  { deep: true },
)

// 生命周期
onMounted(() => {
  // 初始加载
  loadOrders()

  // 启动轮询（如果没有筛选条件）
  nextTick(() => {
    startPolling()
  })
})

// 监听器
watch(dateRange, () => {
  if (dateRange.value) {
    handleSearch()
  }
})
</script>

<style scoped>
.order-management {
  padding: 20px;
  background: #f5f7fa;
  min-height: calc(100vh - 60px);
}

/* 订单查询卡片 - 合并样式 */
.order-query-section {
  margin-bottom: 16px;
  border-radius: 12px;
  border: 1px solid #e4e7ed;
  box-shadow: 0 2px 8px rgb(0 0 0 / 6%);
}

.order-query-section .section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.order-query-section .section-header h2 {
  margin: 0;
  color: var(--el-text-color-primary);
  font-size: 20px;
  font-weight: 600;
}

.polling-status {
  display: flex;
  align-items: center;
  gap: 8px;
}

.polling-indicator {
  animation: pulse-glow 2s infinite;
}

@keyframes pulse-glow {
  0%,
  100% {
    box-shadow: 0 0 0 0 rgb(103 194 58 / 40%);
  }

  50% {
    box-shadow: 0 0 0 4px rgb(103 194 58 / 10%);
  }
}

.order-query-section .el-form {
  padding: 8px 0;
}

.order-query-section .el-form-item {
  margin-bottom: 16px;
  margin-right: 16px;
}

.order-query-section .el-form-item__label {
  width: 80px !important;
  text-align: right;
  padding-right: 12px;
  font-weight: 500;
  color: var(--el-text-color-regular);
}

.order-query-section .el-input,
.order-query-section .el-select,
.order-query-section .el-date-editor {
  width: 200px;
}

.filter-actions {
  display: flex;
  gap: 12px;
  margin-top: 8px;
  padding-left: 0;
  justify-content: flex-start;
}

.error-alert {
  margin-top: 16px;
}

.stats-section {
  margin-bottom: 24px;
}

.order-list {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
  color: var(--el-text-color-primary);
  font-size: 18px;
  font-weight: 600;
}

.loading-section {
  padding: 40px 0;
  text-align: center;
}

.loading-text {
  margin-top: 16px;
  color: var(--el-text-color-regular);
}

.empty-section {
  padding: 60px 0;
  text-align: center;
}

.order-table {
  .compact-table {
    border-radius: 8px;
    overflow: hidden;

    :deep(.el-table__header) {
      background-color: #f8f9fa;
    }

    :deep(.el-table__row) {
      &:hover > td {
        background-color: #f5f7fa !important;
      }

      &.status-updated > td {
        background-color: #f0f9ff !important;
      }
    }
  }

  .order-no-cell {
    display: flex;
    align-items: center;
    gap: 8px;

    .order-number {
      font-family: Monaco, Menlo, 'Ubuntu Mono', monospace;
      font-size: 13px;
      color: #2563eb;
      font-weight: 600;
    }

    .copy-btn {
      opacity: 0;
      transition: opacity 0.2s ease;
    }

    &:hover .copy-btn {
      opacity: 1;
    }
  }

  .applicant-info {
    .name {
      font-weight: 600;
      color: var(--el-text-color-primary);
      margin-bottom: 4px;
    }

    .details {
      font-size: 12px;
      color: var(--el-text-color-secondary);

      .separator {
        margin: 0 6px;
        color: var(--el-border-color);
      }

      .passport {
        font-family: Monaco, Menlo, 'Ubuntu Mono', monospace;
      }
    }
  }

  .time-cell {
    font-size: 13px;
    color: var(--el-text-color-regular);
  }

  .action-buttons {
    display: flex;
    gap: 4px;
    flex-wrap: wrap;
    justify-content: center;

    .el-button {
      padding: 4px 8px;
      font-size: 12px;
    }
  }

  :deep(.el-tag) {
    &.status-updated {
      animation: pulse 2s infinite;
    }
  }
}

@keyframes pulse {
  0%,
  100% {
    box-shadow: 0 0 0 0 rgb(34 197 94 / 40%);
  }

  50% {
    box-shadow: 0 0 0 4px rgb(34 197 94 / 10%);
  }
}

.pagination-section {
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid var(--el-border-color-lighter);
  display: flex;
  justify-content: center;
}

/* 响应式设计 */
@media (width <= 768px) {
  .order-management {
    padding: 12px;
  }

  .order-query-section .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .filter-actions {
    flex-direction: column;
  }

  .filter-actions .el-button {
    width: 100%;
  }

  .order-table .compact-table {
    font-size: 12px;
  }

  .order-table .order-no-cell {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .order-table .applicant-info .details {
    flex-direction: column;
  }

  .order-table .action-buttons {
    flex-direction: column;
    gap: 2px;
  }
}
</style>
