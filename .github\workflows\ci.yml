name: 🚀 CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

env:
  PYTHON_VERSION: "3.11"
  NODE_VERSION: "22.16.0"

jobs:
  # ==========================================
  # Python 后端测试
  # ==========================================
  backend-test:
    name: 🐍 Backend Tests
    runs-on: ubuntu-latest

    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: test_password
          POSTGRES_USER: test_user
          POSTGRES_DB: test_visa_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432

      redis:
        image: redis:7-alpine
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🐍 Setup Python
        uses: actions/setup-python@v4
        with:
          python-version: ${{ env.PYTHON_VERSION }}
          cache: "pip"

      - name: 📦 Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -e ".[development,testing]"

      - name: 🧹 Code quality checks
        run: |
          echo "🔍 Running Ruff checks..."
          ruff check .

          echo "🎨 Running Ruff format check..."
          ruff format --check .

          echo "🔍 Running MyPy type checking..."
          mypy .

      - name: 🔍 Dependency Security Scan
        run: |
          echo "🔍 Running pip-audit dependency scan..."
          pip-audit --desc --disable-pip

      - name: 🛡️ Semgrep Security Analysis (全面扫描)
        uses: semgrep/semgrep-action@v1
        with:
          config: p/python # 全面的 Python 规则集，适合 CI 环境
        env:
          SEMGREP_APP_TOKEN: ${{ secrets.SEMGREP_APP_TOKEN }}

      - name: 🔍 TruffleHog Secret Scanning
        uses: trufflesecurity/trufflehog@main
        with:
          extra_args: --no-update --fail

      - name: 🧪 Run tests
        env:
          DATABASE_URL: postgresql://test_user:test_password@localhost:5432/test_visa_db
          REDIS_URL: redis://localhost:6379/0
          SECRET_KEY: test_secret_key_for_ci
          ENVIRONMENT: testing
        run: |
          pytest --cov=app --cov=backend --cov-report=xml --cov-report=term-missing

      - name: 📊 Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage.xml
          flags: backend
          name: backend-coverage

  # ==========================================
  # 前端测试
  # ==========================================
  frontend-test:
    name: 🌐 Frontend Tests
    runs-on: ubuntu-latest

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📦 Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: "npm"
          cache-dependency-path: "frontend/package-lock.json"

      - name: 📦 Install dependencies
        run: |
          cd frontend
          npm ci

      - name: 🧹 Code quality checks
        run: |
          cd frontend
          echo "🔍 Running ESLint..."
          npm run lint:check

          echo "🎨 Running Stylelint..."
          npm run style:check

          echo "✨ Running Prettier check..."
          npm run format:check

          echo "🔍 Running TypeScript check..."
          npm run type-check

      - name: 🧪 Run unit tests
        run: |
          cd frontend
          npm run test:unit

      - name: 🏗️ Build check
        run: |
          cd frontend
          npm run build

  # ==========================================
  # Docker 构建测试
  # ==========================================
  docker-build:
    name: 🐳 Docker Build Test
    runs-on: ubuntu-latest
    needs: [backend-test, frontend-test]

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🐳 Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: 🏗️ Build main application
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile
          push: false
          tags: visa-automator:test
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: 🏗️ Build Celery worker
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile.celery
          push: false
          tags: visa-automator-celery:test
          cache-from: type=gha
          cache-to: type=gha,mode=max

  # ==========================================
  # 集成测试（可选）
  # ==========================================
  integration-test:
    name: 🔗 Integration Tests
    runs-on: ubuntu-latest
    needs: [docker-build]
    if: github.event_name == 'push' && github.ref == 'refs/heads/main'

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 🐳 Start services
        run: |
          docker-compose -f docker-compose.yml up -d --build

      - name: ⏳ Wait for service health (with retries)
        run: |
          bash .github/scripts/wait-for-health.sh

      - name: 🧪 Run integration tests
        run: |
          # 这里可以添加集成测试
          echo "Integration tests would run here"

      - name: 🧹 Cleanup
        if: always()
        run: |
          docker-compose down -v

  # ==========================================
  # 安全扫描汇总
  # ==========================================
  security-summary:
    name: 🛡️ Security Summary
    runs-on: ubuntu-latest
    needs: [backend-test]
    if: always()

    steps:
      - name: 📥 Checkout code
        uses: actions/checkout@v4

      - name: 📊 Security scan summary
        run: |
          echo "## 🛡️ Security Scan Summary (标准化配置)" >> $GITHUB_STEP_SUMMARY
          echo "| Tool | Status | Description |" >> $GITHUB_STEP_SUMMARY
          echo "|------|--------|-------------|" >> $GITHUB_STEP_SUMMARY
          echo "| TruffleHog | ✅ | 官方Action - 秘密检测 |" >> $GITHUB_STEP_SUMMARY
          echo "| Semgrep | ✅ | 官方Action - 代码安全分析 |" >> $GITHUB_STEP_SUMMARY
          echo "| pip-audit | ✅ | 依赖安全漏洞扫描 |" >> $GITHUB_STEP_SUMMARY
