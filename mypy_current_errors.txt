lint_all.py:169: error: Need type annotation for "optional_files" (hint: "optional_files: list[<type>] = ...")  [var-annotated]
app\services\__init__.py:17: error: Need type annotation for "__all__" (hint: "__all__: list[<type>] = ...")  [var-annotated]
validate_config.py:20: error: Name "tomllib" already defined (by an import)  [no-redef]
database_architecture_audit.py:39: error: Need type annotation for "issues" (hint: "issues: list[<type>] = ...")  [var-annotated]
database_architecture_audit.py:40: error: Need type annotation for "stats" (hint: "stats: dict[<type>, <type>] = ...")  [var-annotated]
database_architecture_audit.py:45: error: Incompatible types in assignment (expression has type "connection", variable has type "None")  [assignment]
database_architecture_audit.py:46: error: "None" has no attribute "cursor"  [attr-defined]
database_architecture_audit.py:64: error: "None" has no attribute "execute"  [attr-defined]
database_architecture_audit.py:66: error: "None" has no attribute "execute"  [attr-defined]
database_architecture_audit.py:67: error: "None" has no attribute "fetchall"  [attr-defined]
database_architecture_audit.py:503: error: Value of type "Collection[Any]" is not indexable  [index]
database_architecture_audit.py:504: error: Value of type "Collection[Any]" is not indexable  [index]
database_architecture_audit.py:505: error: Value of type "Collection[Any]" is not indexable  [index]
backend\config\settings.py:65: note: By default the bodies of untyped functions are not checked, consider using --check-untyped-defs  [annotation-unchecked]
backend\config\settings.py:102: error: Incompatible return value type (got "dict[Any, Any] | None", expected "dict[Any, Any]")  [return-value]
test_container_scaling.py:71: error: Unsupported operand types for + ("object" and "int")  [operator]
test_container_scaling.py:73: error: Unsupported operand types for + ("object" and "int")  [operator]
test_container_scaling.py:75: error: Unsupported operand types for + ("object" and "int")  [operator]
test_container_scaling.py:77: error: Unsupported operand types for + ("object" and "int")  [operator]
scripts\monitor_validation.py:112: error: Unsupported target for indexed assignment ("Collection[str]")  [index]
scripts\monitor_validation.py:121: error: "Collection[str]" has no attribute "append"  [attr-defined]
scripts\monitor_validation.py:153: error: "Collection[str]" has no attribute "append"  [attr-defined]
scripts\monitor_validation.py:175: error: "object" has no attribute "append"  [attr-defined]
scripts\monitor_validation.py:195: error: Unsupported target for indexed assignment ("object")  [index]
scripts\monitor_validation.py:204: error: "object" has no attribute "append"  [attr-defined]
scripts\monitor_validation.py:231: error: "Collection[str]" has no attribute "append"  [attr-defined]
scripts\monitor_validation.py:234: error: "Collection[str]" has no attribute "append"  [attr-defined]
scripts\monitor_validation.py:261: error: Need type annotation for "key_tables" (hint: "key_tables: list[<type>] = ...")  [var-annotated]
scripts\monitor_validation.py:283: error: "Collection[str]" has no attribute "values"  [attr-defined]
scripts\monitor_validation.py:286: error: "Collection[str]" has no attribute "values"  [attr-defined]
scripts\db_performance_comparison.py:106: error: Unsupported operand types for * ("object" and "int")  [operator]
scripts\db_performance_comparison.py:119: error: Unsupported operand types for * ("object" and "int")  [operator]
backend\utils\security.py:309: error: Incompatible types in assignment (expression has type "bytes", variable has type "str | None")  [assignment]
backend\utils\security.py:311: error: Incompatible types in assignment (expression has type "bytes", variable has type "str | None")  [assignment]
backend\utils\security.py:313: error: Argument 1 to "Fernet" has incompatible type "str | None"; expected "bytes | str"  [arg-type]
backend\monitoring\performance_monitor.py:217: error: Need type annotation for "method_counts"  [var-annotated]
backend\monitoring\performance_monitor.py:231: error: Argument "most_used_methods" to "RepositoryMetrics" has incompatible type "list[tuple[Any, Any]]"; expected "list[str]"  [arg-type]
backend\db_config\unified_connection.py:46: note: By default the bodies of untyped functions are not checked, consider using --check-untyped-defs  [annotation-unchecked]
backend\db_config\unified_connection.py:47: note: By default the bodies of untyped functions are not checked, consider using --check-untyped-defs  [annotation-unchecked]
backend\db_config\unified_connection.py:48: note: By default the bodies of untyped functions are not checked, consider using --check-untyped-defs  [annotation-unchecked]
backend\db_config\unified_connection.py:119: error: No overload variant of "sessionmaker" matches argument types "AsyncEngine", "type[AsyncSession]", "bool"  [call-overload]
backend\db_config\unified_connection.py:119: note: Possible overload variants:
backend\db_config\unified_connection.py:119: note:     def [_S: Session] sessionmaker(self, bind: Engine | Connection | None = ..., *, class_: type[_S], autoflush: bool = ..., expire_on_commit: bool = ..., info: dict[Any, Any] | None = ..., **kw: Any) -> sessionmaker[_S]
backend\db_config\unified_connection.py:119: note:     def [_S: Session] sessionmaker(self, bind: Engine | Connection | None = ..., *, autoflush: bool = ..., expire_on_commit: bool = ..., info: dict[Any, Any] | None = ..., **kw: Any) -> sessionmaker[Session]
backend\db_config\unified_connection.py:221: error: "Pool" has no attribute "size"  [attr-defined]
backend\db_config\unified_connection.py:222: error: "Pool" has no attribute "checkedin"  [attr-defined]
backend\db_config\unified_connection.py:223: error: "Pool" has no attribute "checkedout"  [attr-defined]
backend\db_config\unified_connection.py:224: error: "Pool" has no attribute "overflow"  [attr-defined]
backend\db_config\unified_connection.py:225: error: "Pool" has no attribute "invalid"; maybe "_invalidate"?  [attr-defined]
app\repositories\base.py:113: error: Incompatible return value type (got "Sequence[Any]", expected "list[T]")  [return-value]
app\repositories\base.py:120: error: Incompatible return value type (got "int | None", expected "int")  [return-value]
app\utils\env_loader.py:229: error: Incompatible types in assignment (expression has type "dict[Never, Never]", target has type "str")  [assignment]
app\utils\env_loader.py:230: error: Incompatible types in assignment (expression has type "str", variable has type "dict[str, str]")  [assignment]
app\core\user_prefs.py:141: error: Statement is unreachable  [unreachable]
app\batch_processing\contact_parser.py:25: error: Argument 1 to "Document" has incompatible type "Path"; expected "str | IO[bytes] | None"  [arg-type]
config\settings.py:29: error: Item "None" of "str | None" has no attribute "lower"  [union-attr]
config\settings.py:30: error: Argument 1 to "int" has incompatible type "str | None"; expected "str | Buffer | SupportsInt | SupportsIndex | SupportsTrunc"  [arg-type]
app\batch_processing\batch_applicant_importer.py:113: error: Item "str" of "Path | str | Any" has no attribute "stem"  [union-attr]
app\batch_processing\batch_applicant_importer.py:146: error: Argument "visa_validity_duration" to "VietnamEVisaApplicant" has incompatible type "Path | str | Any"; expected "str | None"  [arg-type]
app\batch_processing\batch_applicant_importer.py:147: error: Argument "visa_entry_type" to "VietnamEVisaApplicant" has incompatible type "Path | str | Any"; expected "str | None"  [arg-type]
app\batch_processing\batch_applicant_importer.py:148: error: Argument "intended_entry_gate" to "VietnamEVisaApplicant" has incompatible type "Path | str | Any"; expected "str | None"  [arg-type]
app\batch_processing\batch_applicant_importer.py:149: error: Argument "visa_start_date" to "VietnamEVisaApplicant" has incompatible type "Path | str | Any"; expected "str | None"  [arg-type]
config\browser_config.py:45: error: Statement is unreachable  [unreachable]
config\browser_config.py:125: error: Argument 1 to "new_context" of "Browser" has incompatible type "**dict[str, dict[str, Any]]"; expected "ViewportSize | None"  [arg-type]
config\browser_config.py:125: error: Argument 1 to "new_context" of "Browser" has incompatible type "**dict[str, dict[str, Any]]"; expected "bool | None"  [arg-type]
config\browser_config.py:125: error: Argument 1 to "new_context" of "Browser" has incompatible type "**dict[str, dict[str, Any]]"; expected "str | None"  [arg-type]
config\browser_config.py:125: error: Argument 1 to "new_context" of "Browser" has incompatible type "**dict[str, dict[str, Any]]"; expected "Geolocation | None"  [arg-type]
config\browser_config.py:125: error: Argument 1 to "new_context" of "Browser" has incompatible type "**dict[str, dict[str, Any]]"; expected "Sequence[str] | None"  [arg-type]
config\browser_config.py:125: error: Argument 1 to "new_context" of "Browser" has incompatible type "**dict[str, dict[str, Any]]"; expected "HttpCredentials | None"  [arg-type]
config\browser_config.py:125: error: Argument 1 to "new_context" of "Browser" has incompatible type "**dict[str, dict[str, Any]]"; expected "float | None"  [arg-type]
config\browser_config.py:125: error: Argument 1 to "new_context" of "Browser" has incompatible type "**dict[str, dict[str, Any]]"; expected "Literal['dark', 'light', 'no-preference', 'null'] | None"  [arg-type]
config\browser_config.py:125: error: Argument 1 to "new_context" of "Browser" has incompatible type "**dict[str, dict[str, Any]]"; expected "Literal['no-preference', 'null', 'reduce'] | None"  [arg-type]
config\browser_config.py:125: error: Argument 1 to "new_context" of "Browser" has incompatible type "**dict[str, dict[str, Any]]"; expected "Literal['active', 'none', 'null'] | None"  [arg-type]
config\browser_config.py:125: error: Argument 1 to "new_context" of "Browser" has incompatible type "**dict[str, dict[str, Any]]"; expected "Literal['more', 'no-preference', 'null'] | None"  [arg-type]
config\browser_config.py:125: error: Argument 1 to "new_context" of "Browser" has incompatible type "**dict[str, dict[str, Any]]"; expected "ProxySettings | None"  [arg-type]
config\browser_config.py:125: error: Argument 1 to "new_context" of "Browser" has incompatible type "**dict[str, dict[str, Any]]"; expected "str | Path | None"  [arg-type]
config\browser_config.py:125: error: Argument 1 to "new_context" of "Browser" has incompatible type "**dict[str, dict[str, Any]]"; expected "StorageState | str | Path | None"  [arg-type]
config\browser_config.py:125: error: Argument 1 to "new_context" of "Browser" has incompatible type "**dict[str, dict[str, Any]]"; expected "Literal['allow', 'block'] | None"  [arg-type]
config\browser_config.py:125: error: Argument 1 to "new_context" of "Browser" has incompatible type "**dict[str, dict[str, Any]]"; expected "str | Pattern[str] | None"  [arg-type]
config\browser_config.py:125: error: Argument 1 to "new_context" of "Browser" has incompatible type "**dict[str, dict[str, Any]]"; expected "Literal['full', 'minimal'] | None"  [arg-type]
config\browser_config.py:125: error: Argument 1 to "new_context" of "Browser" has incompatible type "**dict[str, dict[str, Any]]"; expected "Literal['attach', 'embed', 'omit'] | None"  [arg-type]
config\browser_config.py:125: error: Argument 1 to "new_context" of "Browser" has incompatible type "**dict[str, dict[str, Any]]"; expected "list[ClientCertificate] | None"  [arg-type]
app\utils\captcha_solver.py:112: error: Incompatible types in assignment (expression has type "str", variable has type "None")  [assignment]
app\utils\captcha_solver.py:118: error: Incompatible types in assignment (expression has type "str", variable has type "None")  [assignment]
app\utils\captcha_solver.py:129: error: Incompatible types in assignment (expression has type "str", variable has type "None")  [assignment]
app\utils\captcha_solver.py:182: error: Incompatible types in assignment (expression has type "str", variable has type "None")  [assignment]
app\utils\captcha_solver.py:202: error: Incompatible types in assignment (expression has type "str", variable has type "None")  [assignment]
app\utils\captcha_solver.py:207: error: Incompatible types in assignment (expression has type "str", variable has type "None")  [assignment]
app\utils\captcha_solver.py:211: error: Incompatible types in assignment (expression has type "str", variable has type "None")  [assignment]
app\repositories\order_repository.py:90: error: Incompatible return value type (got "Sequence[Order]", expected "list[Order]")  [return-value]
app\repositories\order_repository.py:102: error: Incompatible return value type (got "int | None", expected "int")  [return-value]
app\repositories\order_repository.py:131: error: Incompatible return value type (got "Sequence[Order]", expected "list[Order]")  [return-value]
app\repositories\order_repository.py:156: error: Incompatible return value type (got "Sequence[Order]", expected "list[Order]")  [return-value]
app\repositories\order_repository.py:180: error: Incompatible types in assignment (expression has type "str", variable has type "Column[str]")  [assignment]
app\repositories\order_repository.py:181: error: Incompatible types in assignment (expression has type "datetime", variable has type "Column[datetime]")  [assignment]
app\repositories\order_repository.py:275: error: Incompatible types in assignment (expression has type "ColumnElement[bool]", variable has type "BinaryExpression[bool]")  [assignment]
app\repositories\order_repository.py:281: error: Incompatible types in assignment (expression has type "ColumnElement[bool]", variable has type "BinaryExpression[bool]")  [assignment]
app\repositories\order_repository.py:315: error: Unsupported operand types for + ("None" and "int")  [operator]
app\repositories\order_repository.py:315: note: Both left and right operands are unions
app\repositories\order_repository.py:380: error: Incompatible types in assignment (expression has type "Any | str", variable has type "Column[str]")  [assignment]
app\repositories\order_repository.py:381: error: Incompatible types in assignment (expression has type "datetime", variable has type "Column[datetime]")  [assignment]
app\repositories\order_repository.py:490: error: Incompatible return value type (got "Sequence[Row[Any]]", expected "list[dict[str, Any]]")  [return-value]
app\repositories\automation_logs_repository.py:38: error: Incompatible return value type (got "Sequence[AutomationLogs]", expected "list[AutomationLogs]")  [return-value]
app\repositories\automation_logs_repository.py:47: error: Incompatible return value type (got "Sequence[AutomationLogs]", expected "list[AutomationLogs]")  [return-value]
app\repositories\automation_logs_repository.py:76: error: Incompatible return value type (got "Sequence[AutomationLogs]", expected "list[AutomationLogs]")  [return-value]
app\repositories\automation_logs_repository.py:103: error: Incompatible return value type (got "Sequence[AutomationLogs]", expected "list[AutomationLogs]")  [return-value]
app\repositories\automation_logs_repository.py:191: error: Need type annotation for "status_stats" (hint: "status_stats: dict[<type>, <type>] = ...")  [var-annotated]
app\repositories\automation_logs_repository.py:191: error: Argument 1 to "dict" has incompatible type "Sequence[Row[tuple[str, int]]]"; expected "Iterable[tuple[Never, Never]]"  [arg-type]
app\repositories\automation_logs_repository.py:199: error: Need type annotation for "type_stats" (hint: "type_stats: dict[<type>, <type>] = ...")  [var-annotated]
app\repositories\automation_logs_repository.py:199: error: Argument 1 to "dict" has incompatible type "Sequence[Row[tuple[str, int]]]"; expected "Iterable[tuple[Never, Never]]"  [arg-type]
app\repositories\application_repository.py:74: error: Incompatible return value type (got "Sequence[Application]", expected "list[Application]")  [return-value]
app\repositories\application_repository.py:90: error: Incompatible return value type (got "int | None", expected "int")  [return-value]
app\repositories\application_repository.py:104: error: Incompatible return value type (got "Sequence[Application]", expected "list[Application]")  [return-value]
app\repositories\application_repository.py:118: error: Incompatible return value type (got "Sequence[Application]", expected "list[Application]")  [return-value]
app\repositories\applicant_repository.py:62: error: Incompatible return value type (got "Sequence[Applicant]", expected "list[Applicant]")  [return-value]
app\repositories\applicant_repository.py:69: error: Incompatible return value type (got "int | None", expected "int")  [return-value]
app\repositories\applicant_repository.py:111: error: Incompatible return value type (got "Sequence[Applicant]", expected "list[Applicant]")  [return-value]
app\repositories\applicant_repository.py:134: error: Incompatible return value type (got "Sequence[Applicant]", expected "list[Applicant]")  [return-value]
app\repositories\applicant_repository.py:157: error: Incompatible return value type (got "Sequence[Applicant]", expected "list[Applicant]")  [return-value]
app\repositories\applicant_repository.py:202: error: Need type annotation for "nationality_stats" (hint: "nationality_stats: dict[<type>, <type>] = ...")  [var-annotated]
app\repositories\applicant_repository.py:202: error: Argument 1 to "dict" has incompatible type "Sequence[Row[tuple[str, int]]]"; expected "Iterable[tuple[Never, Never]]"  [arg-type]
app\repositories\applicant_repository.py:210: error: Need type annotation for "gender_stats" (hint: "gender_stats: dict[<type>, <type>] = ...")  [var-annotated]
app\repositories\applicant_repository.py:210: error: Argument 1 to "dict" has incompatible type "Sequence[Row[tuple[str, int]]]"; expected "Iterable[tuple[Never, Never]]"  [arg-type]
tests\conftest.py:60: error: "Session" has no attribute "__aenter__"; maybe "__enter__"?  [attr-defined]
tests\conftest.py:60: error: "Session" has no attribute "__aexit__"; maybe "__exit__"?  [attr-defined]
app\utils\retry_strategy.py:49: error: Item "None" of "Future | None" has no attribute "failed"  [union-attr]
app\utils\retry_strategy.py:50: error: Item "None" of "Future | None" has no attribute "exception"  [union-attr]
app\utils\retry_strategy.py:57: error: Item "stop_base" of "stop_base | Callable[[RetryCallState], bool]" has no attribute "max_attempt_number"  [union-attr]
app\utils\retry_strategy.py:57: error: Item "function" of "stop_base | Callable[[RetryCallState], bool]" has no attribute "max_attempt_number"  [union-attr]
app\utils\retry_strategy.py:57: error: Item "None" of "RetryAction | None" has no attribute "sleep"  [union-attr]
app\utils\retry_strategy.py:62: error: Item "stop_base" of "stop_base | Callable[[RetryCallState], bool]" has no attribute "max_attempt_number"  [union-attr]
app\utils\retry_strategy.py:62: error: Item "function" of "stop_base | Callable[[RetryCallState], bool]" has no attribute "max_attempt_number"  [union-attr]
app\utils\retry_strategy.py:62: error: Item "None" of "RetryAction | None" has no attribute "sleep"  [union-attr]
app\utils\retry_strategy.py:67: error: Item "stop_base" of "stop_base | Callable[[RetryCallState], bool]" has no attribute "max_attempt_number"  [union-attr]
app\utils\retry_strategy.py:67: error: Item "function" of "stop_base | Callable[[RetryCallState], bool]" has no attribute "max_attempt_number"  [union-attr]
app\utils\retry_strategy.py:67: error: Item "None" of "RetryAction | None" has no attribute "sleep"  [union-attr]
app\utils\retry_strategy.py:72: error: Item "stop_base" of "stop_base | Callable[[RetryCallState], bool]" has no attribute "max_attempt_number"  [union-attr]
app\utils\retry_strategy.py:72: error: Item "function" of "stop_base | Callable[[RetryCallState], bool]" has no attribute "max_attempt_number"  [union-attr]
app\utils\retry_strategy.py:72: error: Item "None" of "RetryAction | None" has no attribute "sleep"  [union-attr]
app\utils\retry_strategy.py:77: error: Item "stop_base" of "stop_base | Callable[[RetryCallState], bool]" has no attribute "max_attempt_number"  [union-attr]
app\utils\retry_strategy.py:77: error: Item "function" of "stop_base | Callable[[RetryCallState], bool]" has no attribute "max_attempt_number"  [union-attr]
app\utils\retry_strategy.py:77: error: Item "None" of "RetryAction | None" has no attribute "sleep"  [union-attr]
app\utils\retry_strategy.py:82: error: Item "stop_base" of "stop_base | Callable[[RetryCallState], bool]" has no attribute "max_attempt_number"  [union-attr]
app\utils\retry_strategy.py:82: error: Item "function" of "stop_base | Callable[[RetryCallState], bool]" has no attribute "max_attempt_number"  [union-attr]
app\utils\retry_strategy.py:82: error: Item "None" of "RetryAction | None" has no attribute "sleep"  [union-attr]
app\utils\retry_strategy.py:87: error: Item "None" of "Future | None" has no attribute "result"  [union-attr]
app\utils\retry_strategy.py:89: error: Item "stop_base" of "stop_base | Callable[[RetryCallState], bool]" has no attribute "max_attempt_number"  [union-attr]
app\utils\retry_strategy.py:89: error: Item "function" of "stop_base | Callable[[RetryCallState], bool]" has no attribute "max_attempt_number"  [union-attr]
app\utils\retry_strategy.py:89: error: Item "None" of "RetryAction | None" has no attribute "sleep"  [union-attr]
app\utils\retry_strategy.py:96: error: Item "None" of "Future | None" has no attribute "failed"  [union-attr]
app\utils\retry_strategy.py:97: error: Item "None" of "Future | None" has no attribute "exception"  [union-attr]
app\utils\retry_strategy.py:104: error: Item "stop_base" of "stop_base | Callable[[RetryCallState], bool]" has no attribute "max_attempt_number"  [union-attr]
app\utils\retry_strategy.py:104: error: Item "function" of "stop_base | Callable[[RetryCallState], bool]" has no attribute "max_attempt_number"  [union-attr]
app\utils\retry_strategy.py:104: error: Item "None" of "RetryAction | None" has no attribute "sleep"  [union-attr]
app\utils\retry_strategy.py:109: error: Item "stop_base" of "stop_base | Callable[[RetryCallState], bool]" has no attribute "max_attempt_number"  [union-attr]
app\utils\retry_strategy.py:109: error: Item "function" of "stop_base | Callable[[RetryCallState], bool]" has no attribute "max_attempt_number"  [union-attr]
app\utils\retry_strategy.py:109: error: Item "None" of "RetryAction | None" has no attribute "sleep"  [union-attr]
app\utils\retry_strategy.py:114: error: Item "stop_base" of "stop_base | Callable[[RetryCallState], bool]" has no attribute "max_attempt_number"  [union-attr]
app\utils\retry_strategy.py:114: error: Item "function" of "stop_base | Callable[[RetryCallState], bool]" has no attribute "max_attempt_number"  [union-attr]
app\utils\retry_strategy.py:114: error: Item "None" of "RetryAction | None" has no attribute "sleep"  [union-attr]
app\utils\retry_strategy.py:119: error: Item "stop_base" of "stop_base | Callable[[RetryCallState], bool]" has no attribute "max_attempt_number"  [union-attr]
app\utils\retry_strategy.py:119: error: Item "function" of "stop_base | Callable[[RetryCallState], bool]" has no attribute "max_attempt_number"  [union-attr]
app\utils\retry_strategy.py:119: error: Item "None" of "RetryAction | None" has no attribute "sleep"  [union-attr]
app\utils\retry_strategy.py:124: error: Item "None" of "Future | None" has no attribute "result"  [union-attr]
app\utils\retry_strategy.py:126: error: Item "stop_base" of "stop_base | Callable[[RetryCallState], bool]" has no attribute "max_attempt_number"  [union-attr]
app\utils\retry_strategy.py:126: error: Item "function" of "stop_base | Callable[[RetryCallState], bool]" has no attribute "max_attempt_number"  [union-attr]
app\utils\retry_strategy.py:126: error: Item "None" of "RetryAction | None" has no attribute "sleep"  [union-attr]
app\fillers\vietnam_filler.py:59: note: By default the bodies of untyped functions are not checked, consider using --check-untyped-defs  [annotation-unchecked]
app\fillers\vietnam_filler.py:60: note: By default the bodies of untyped functions are not checked, consider using --check-untyped-defs  [annotation-unchecked]
app\fillers\vietnam_filler.py:61: note: By default the bodies of untyped functions are not checked, consider using --check-untyped-defs  [annotation-unchecked]
app\fillers\vietnam_filler.py:62: note: By default the bodies of untyped functions are not checked, consider using --check-untyped-defs  [annotation-unchecked]
app\fillers\vietnam_filler.py:81: error: Incompatible types in assignment (expression has type "Any | None", variable has type "str")  [assignment]
app\fillers\vietnam_filler.py:298: error: Argument 1 to "wait_for_selector" of "Page" has incompatible type "str | None"; expected "str"  [arg-type]
app\fillers\vietnam_filler.py:366: error: Argument 1 to "join" of "str" has incompatible type "list[str | None]"; expected "Iterable[str]"  [arg-type]
app\fillers\vietnam_filler.py:455: error: Argument 1 to "fill" of "Page" has incompatible type "str | None"; expected "str"  [arg-type]
app\fillers\vietnam_filler.py:471: error: Argument 1 to "wait_for_selector" of "Page" has incompatible type "str | None"; expected "str"  [arg-type]
app\fillers\vietnam_filler.py:518: error: Argument 1 to "wait_for_selector" of "Page" has incompatible type "str | None"; expected "str"  [arg-type]
app\fillers\vietnam_filler.py:535: error: Argument 1 to "locator" of "Page" has incompatible type "str | None"; expected "str"  [arg-type]
app\fillers\vietnam_filler.py:596: error: Argument 1 to "locator" of "Page" has incompatible type "str | None"; expected "str"  [arg-type]
app\fillers\vietnam_filler.py:605: error: Argument 1 to "locator" of "Page" has incompatible type "str | None"; expected "str"  [arg-type]
app\fillers\vietnam_filler.py:612: error: Argument 1 to "locator" of "Page" has incompatible type "str | None"; expected "str"  [arg-type]
app\fillers\vietnam_filler.py:865: error: Argument 1 to "locator" of "Page" has incompatible type "str | None"; expected "str"  [arg-type]
app\fillers\vietnam_filler.py:874: error: Argument 1 to "locator" of "Page" has incompatible type "str | None"; expected "str"  [arg-type]
app\fillers\vietnam_filler.py:1301: error: Argument 1 to "locator" of "Page" has incompatible type "str | None"; expected "str"  [arg-type]
app\fillers\vietnam_filler.py:1324: error: Argument 1 to "locator" of "Page" has incompatible type "str | None"; expected "str"  [arg-type]
app\fillers\vietnam_filler.py:1332: error: Argument 1 to "locator" of "Page" has incompatible type "str | None"; expected "str"  [arg-type]
app\fillers\vietnam_filler.py:1427: error: Argument 1 to "locator" of "Page" has incompatible type "str | None"; expected "str"  [arg-type]
app\fillers\vietnam_filler.py:1431: error: Argument 1 to "locator" of "Page" has incompatible type "str | None"; expected "str"  [arg-type]
app\fillers\vietnam_filler.py:1450: error: Argument 1 to "locator" of "Page" has incompatible type "str | None"; expected "str"  [arg-type]
app\fillers\vietnam_filler.py:1454: error: Argument 1 to "locator" of "Page" has incompatible type "str | None"; expected "str"  [arg-type]
app\fillers\vietnam_filler.py:1462: error: Argument 1 to "locator" of "Page" has incompatible type "str | None"; expected "str"  [arg-type]
app\fillers\vietnam_filler.py:1478: error: Argument 1 to "locator" of "Page" has incompatible type "str | None"; expected "str"  [arg-type]
app\fillers\vietnam_filler.py:1573: error: Argument 1 to "locator" of "Page" has incompatible type "str | None"; expected "str"  [arg-type]
app\fillers\vietnam_filler.py:1582: error: Argument 1 to "locator" of "Page" has incompatible type "str | None"; expected "str"  [arg-type]
app\fillers\vietnam_filler.py:1593: error: Argument 1 to "locator" of "Page" has incompatible type "str | None"; expected "str"  [arg-type]
app\fillers\vietnam_filler.py:1642: error: Argument 1 to "wait_for_selector" of "Page" has incompatible type "str | None"; expected "str"  [arg-type]
app\fillers\vietnam_filler.py:1692: error: Argument 1 to "fill" of "Page" has incompatible type "str | None"; expected "str"  [arg-type]
app\fillers\vietnam_filler.py:1696: error: Argument 1 to "click" of "Page" has incompatible type "str | None"; expected "str"  [arg-type]
app\email\unified_email_parser.py:357: error: Value of type "PageElement" is not indexable  [index]
app\downloader\parse_vn_evisa_pdf.py:32: error: Incompatible types in assignment (expression has type "Path", variable has type "str")  [assignment]
app\downloader\parse_vn_evisa_pdf.py:75: error: Argument 1 to "guess_visa_days" has incompatible type "str | Any | None"; expected "str"  [arg-type]
app\downloader\parse_vn_evisa_pdf.py:75: error: Argument 2 to "guess_visa_days" has incompatible type "str | Any | None"; expected "str"  [arg-type]
app\core\visa_automation_engine.py:79: error: Missing return statement  [return]
app\core\visa_automation_engine.py:232: error: Statement is unreachable  [unreachable]
test_email_visa_download.py:146: error: Cannot find implementation or library stub for module named "app.email.result_handler"  [import-not-found]
test_email_visa_download.py:146: note: See https://mypy.readthedocs.io/en/stable/running_mypy.html#missing-imports
celery_worker\tasks.py:26: error: Need type annotation for "current_task_info" (hint: "current_task_info: dict[<type>, <type>] = ...")  [var-annotated]
celery_worker\tasks.py:229: error: "VietnamEVisaApplicant" has no attribute "force_resubmit"  [attr-defined]
app\email\unified_email_processor.py:65: error: Argument 1 to "_process_submission_email" of "UnifiedEmailProcessor" has incompatible type "dict[str, Any] | None"; expected "dict[str, Any]"  [arg-type]
app\email\unified_email_processor.py:67: error: Argument 1 to "_process_payment_email" of "UnifiedEmailProcessor" has incompatible type "dict[str, Any] | None"; expected "dict[str, Any]"  [arg-type]
app\email\unified_email_processor.py:69: error: Argument 1 to "_process_result_email" of "UnifiedEmailProcessor" has incompatible type "dict[str, Any] | None"; expected "dict[str, Any]"  [arg-type]
app\batch_processing\batch_runner.py:58: error: Need type annotation for "processes" (hint: "processes: list[<type>] = ...")  [var-annotated]
run_batch_main.py:10: error: Unexpected keyword argument "file_path" for "setup_logger"  [call-arg]
docker_batch.py:94: error: Item "TextIO" of "TextIO | Any" has no attribute "reconfigure"  [union-attr]
app\email\unified_email_service.py:89: note: By default the bodies of untyped functions are not checked, consider using --check-untyped-defs  [annotation-unchecked]
app\email\unified_email_service.py:90: note: By default the bodies of untyped functions are not checked, consider using --check-untyped-defs  [annotation-unchecked]
app\email\unified_email_service.py:91: note: By default the bodies of untyped functions are not checked, consider using --check-untyped-defs  [annotation-unchecked]
app\email\unified_email_service.py:92: note: By default the bodies of untyped functions are not checked, consider using --check-untyped-defs  [annotation-unchecked]
app\email\unified_email_service.py:187: error: Need type annotation for "messages" (hint: "messages: list[<type>] = ...")  [var-annotated]
app\email\unified_email_service.py:216: error: Item "Message[str, str]" of "Message[str, str] | Any | bytes" has no attribute "decode"  [union-attr]
app\email\unified_email_service.py:325: error: Item "None" of "Any | None" has no attribute "get_job"  [union-attr]
app\email\unified_email_service.py:326: error: Item "None" of "Any | None" has no attribute "remove_job"  [union-attr]
app\email\unified_email_service.py:329: error: Item "None" of "Any | None" has no attribute "add_job"  [union-attr]
backend\middleware\security.py:20: error: Need type annotation for "requests"  [var-annotated]
backend\middleware\security.py:24: error: Item "None" of "Address | None" has no attribute "host"  [union-attr]
backend\middleware\security.py:56: error: Incompatible types in assignment (expression has type "int", variable has type "str | None")  [assignment]
backend\middleware\security.py:57: error: Unsupported operand types for > ("str" and "int")  [operator]
backend\auth_fastapi_users\manager.py:52: error: Signature of "authenticate" incompatible with supertype "BaseUserManager"  [override]
backend\auth_fastapi_users\manager.py:52: note:      Superclass:
backend\auth_fastapi_users\manager.py:52: note:          def authenticate(self, credentials: OAuth2PasswordRequestForm) -> Coroutine[Any, Any, User | None]
backend\auth_fastapi_users\manager.py:52: note:      Subclass:
backend\auth_fastapi_users\manager.py:52: note:          def authenticate(self, credentials: str | None, password: str, request: Request | None = ...) -> Coroutine[Any, Any, User | None]
backend\auth_fastapi_users\manager.py:76: error: "BaseUserDatabase[User, UUID]" has no attribute "session"  [attr-defined]
backend\auth_fastapi_users\manager.py:89: error: Argument 2 to "verify_and_update" of "PasswordHelperProtocol" has incompatible type "Column[str]"; expected "str"  [arg-type]
backend\routes\email_processing.py:130: error: Missing named argument "application_id" for "EmailProcessingResponse"  [call-arg]
backend\routes\email_processing.py:130: error: Missing named argument "updated_fields" for "EmailProcessingResponse"  [call-arg]
backend\routes\email_processing.py:200: error: Missing named argument "application_id" for "EmailProcessingResponse"  [call-arg]
backend\routes\email_processing.py:200: error: Missing named argument "updated_fields" for "EmailProcessingResponse"  [call-arg]
backend\routes\email_processing.py:270: error: Missing named argument "application_id" for "EmailProcessingResponse"  [call-arg]
backend\routes\email_processing.py:270: error: Missing named argument "updated_fields" for "EmailProcessingResponse"  [call-arg]
backend\auth_fastapi_users\user_management.py:16: error: Missing return statement  [return]
backend\auth_fastapi_users\user_management.py:93: error: Missing return statement  [return]
backend\auth_fastapi_users\user_management.py:160: error: Incompatible types in assignment (expression has type "bool", variable has type "Column[bool]")  [assignment]
backend\auth_fastapi_users\user_management.py:161: error: Incompatible types in assignment (expression has type "str", variable has type "Column[str]")  [assignment]
backend\routes\automation_logs.py:145: error: Incompatible types in assignment (expression has type "AutomationLogs | None", variable has type "AutomationLogs")  [assignment]
backend\routes\order.py:92: error: Unexpected keyword argument "application_number" for "OrderQueryParams"  [call-arg]
backend\routes\order.py:240: error: Unexpected keyword argument "application_number" for "OrderQueryParams"  [call-arg]
backend\routes\order.py:244: error: Argument 1 to "query_user_orders" of "OrderService" has incompatible type "UUID"; expected "int"  [arg-type]
backend\routes\visa\status.py:37: error: "UnifiedDatabaseManager" has no attribute "query_applications_by_identifier"  [attr-defined]
backend\routes\visa\status.py:172: error: "UnifiedDatabaseManager" has no attribute "get_user_applications"  [attr-defined]
backend\routes\visa\export.py:203: error: Incompatible types in assignment (expression has type "date", target has type "str")  [assignment]
backend\routes\visa\export.py:211: error: Incompatible types in assignment (expression has type "date", target has type "str")  [assignment]
backend\routes\visa\export.py:289: error: Incompatible return value type (got "Sequence[Row[Any]]", expected "list[Any]")  [return-value]
backend\routes\visa\application.py:59: error: Incompatible return value type (got "tuple[None, None]", expected "tuple[str, str]")  [return-value]
backend\routes\visa\application.py:72: error: Incompatible return value type (got "tuple[None, None]", expected "tuple[str, str]")  [return-value]
backend\routes\visa\application.py:77: error: Incompatible return value type (got "tuple[Column[str], Column[str]]", expected "tuple[str, str]")  [return-value]
backend\routes\visa\application.py:81: error: Incompatible return value type (got "tuple[None, None]", expected "tuple[str, str]")  [return-value]
backend\routes\visa\application.py:208: error: Argument "tracking_info" to "VisaApplicationResponse" has incompatible type "dict[str, Any]"; expected "TrackingInfo | None"  [arg-type]
backend\routes\visa\application.py:333: error: Item "None" of "str | None" has no attribute "strip"  [union-attr]
backend\routes\visa\application.py:359: error: Argument "tracking_info" to "VisaApplicationResponse" has incompatible type "dict[str, Any]"; expected "TrackingInfo | None"  [arg-type]
backend\routes\visa\application.py:395: error: Unsupported target for indexed assignment ("object")  [index]
backend\routes\visa\application.py:419: error: Unsupported target for indexed assignment ("object")  [index]
backend\routes\visa\application.py:440: error: Incompatible types in assignment (expression has type "Result[tuple[UUID]]", variable has type "dict[str, Any]")  [assignment]
backend\routes\visa\application.py:441: error: "dict[str, Any]" has no attribute "scalar_one"  [attr-defined]
backend\routes\visa\application.py:476: error: Incompatible types in assignment (expression has type "str | None", variable has type "Column[str]")  [assignment]
backend\routes\visa\application.py:477: error: Incompatible types in assignment (expression has type "str | None", variable has type "Column[str]")  [assignment]
backend\routes\visa\application.py:478: error: Incompatible types in assignment (expression has type "str | None", variable has type "Column[str]")  [assignment]
backend\routes\visa\application.py:479: error: Incompatible types in assignment (expression has type "str", variable has type "Column[str]")  [assignment]
backend\routes\visa\application.py:480: error: Incompatible types in assignment (expression has type "str", variable has type "Column[str]")  [assignment]
backend\routes\visa\application.py:481: error: Incompatible types in assignment (expression has type "date", variable has type "Column[date]")  [assignment]
backend\routes\visa\application.py:482: error: Incompatible types in assignment (expression has type "str | None", variable has type "Column[str]")  [assignment]
backend\routes\visa\application.py:483: error: Incompatible types in assignment (expression has type "str | None", variable has type "Column[str]")  [assignment]
backend\routes\visa\application.py:484: error: Incompatible types in assignment (expression has type "str | None", variable has type "Column[str]")  [assignment]
backend\routes\visa\application.py:485: error: Incompatible types in assignment (expression has type "str | None", variable has type "Column[str]")  [assignment]
backend\routes\visa\application.py:486: error: Incompatible types in assignment (expression has type "str | None", variable has type "Column[str]")  [assignment]
backend\routes\visa\application.py:489: error: Incompatible types in assignment (expression has type "str | None", variable has type "Column[str]")  [assignment]
backend\routes\visa\application.py:490: error: Incompatible types in assignment (expression has type "Any | None", variable has type "Column[str]")  [assignment]
backend\routes\visa\application.py:493: error: Incompatible types in assignment (expression has type "Any | None", variable has type "Column[str]")  [assignment]
backend\routes\visa\application.py:496: error: Incompatible types in assignment (expression has type "str | None", variable has type "Column[str]")  [assignment]
backend\routes\visa\application.py:497: error: Incompatible types in assignment (expression has type "str | None", variable has type "Column[str]")  [assignment]
backend\routes\visa\application.py:498: error: Incompatible types in assignment (expression has type "str | None", variable has type "Column[str]")  [assignment]
backend\routes\visa\application.py:499: error: Incompatible types in assignment (expression has type "datetime", variable has type "Column[datetime]")  [assignment]
backend\routes\visa\application.py:604: error: Argument "tracking_info" to "VisaApplicationResponse" has incompatible type "dict[str, Any]"; expected "TrackingInfo | None"  [arg-type]
backend\routes\visa\admin.py:96: error: "UnifiedDatabaseManager" has no attribute "query_applications_with_filters"  [attr-defined]
backend\routes\visa\admin.py:101: error: "UnifiedDatabaseManager" has no attribute "count_applications_with_filters"  [attr-defined]
backend\routes\visa\admin.py:199: error: "UnifiedDatabaseManager" has no attribute "query_applications_by_identifier"  [attr-defined]
backend\main.py:129: error: Argument 2 to "add_exception_handler" of "Starlette" has incompatible type "Callable[[Request, VisaApplicationError], Coroutine[Any, Any, Any]]"; expected "Callable[[Request, Exception], Response | Awaitable[Response]] | Callable[[WebSocket, Exception], Awaitable[None]]"  [arg-type]
backend\main.py:130: error: Argument 2 to "add_exception_handler" of "Starlette" has incompatible type "Callable[[Request, OCRProcessingError], Coroutine[Any, Any, Any]]"; expected "Callable[[Request, Exception], Response | Awaitable[Response]] | Callable[[WebSocket, Exception], Awaitable[None]]"  [arg-type]
backend\main.py:131: error: Argument 2 to "add_exception_handler" of "Starlette" has incompatible type "Callable[[Request, FileProcessingError], Coroutine[Any, Any, Any]]"; expected "Callable[[Request, Exception], Response | Awaitable[Response]] | Callable[[WebSocket, Exception], Awaitable[None]]"  [arg-type]
backend\main.py:132: error: Argument 2 to "add_exception_handler" of "Starlette" has incompatible type "Callable[[Request, AuthenticationError], Coroutine[Any, Any, Any]]"; expected "Callable[[Request, Exception], Response | Awaitable[Response]] | Callable[[WebSocket, Exception], Awaitable[None]]"  [arg-type]
backend\main.py:133: error: Argument 2 to "add_exception_handler" of "Starlette" has incompatible type "Callable[[Request, RequestValidationError], Coroutine[Any, Any, Any]]"; expected "Callable[[Request, Exception], Response | Awaitable[Response]] | Callable[[WebSocket, Exception], Awaitable[None]]"  [arg-type]
backend\main.py:134: error: Argument 2 to "add_exception_handler" of "Starlette" has incompatible type "Callable[[Request, HTTPException], Coroutine[Any, Any, Any]]"; expected "Callable[[Request, Exception], Response | Awaitable[Response]] | Callable[[WebSocket, Exception], Awaitable[None]]"  [arg-type]
backend\main.py:225: error: Incompatible types in assignment (expression has type "None", variable has type "VisaAutomationEngine")  [assignment]
