#!/usr/bin/env python3
"""
监控数据验证脚本
用于验证统一连接池监控与原生监控的数据一致性
✅ 按照GG Notepad要求：全量对账、可追溯、详细日志
"""

import asyncio
from datetime import datetime
import json
from pathlib import Path
import subprocess  # nosec B404 # 需要subprocess来运行监控脚本
import sys
from typing import Any

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent.parent))


class MonitorValidation:
    """监控数据验证器"""

    def __init__(self):
        self.validation_results = {}
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    async def run_legacy_monitor(self) -> dict[str, Any]:
        """运行原生监控脚本"""
        print("🔍 运行原生监控脚本...")

        try:
            # 运行原生监控脚本
            result = subprocess.run(  # nosec B603 # 受控的监控脚本执行
                [sys.executable, "scripts/db_performance_monitor.py"],
                capture_output=True,
                text=True,
                timeout=60,
            )

            if result.returncode != 0:
                print(f"❌ 原生监控脚本执行失败: {result.stderr}")
                return {"error": result.stderr}

            # 查找最新的报告文件
            reports_dir = Path("reports")
            if reports_dir.exists():
                json_files = list(reports_dir.glob("postgresql_performance_*.json"))
                if json_files:
                    latest_file = max(json_files, key=lambda x: x.stat().st_mtime)
                    with open(latest_file, encoding="utf-8") as f:
                        return json.load(f)

            return {"error": "未找到原生监控报告文件"}

        except Exception as e:
            return {"error": str(e)}

    async def run_unified_monitor(self) -> dict[str, Any]:
        """运行统一连接池监控脚本"""
        print("🔍 运行统一连接池监控脚本...")

        try:
            # 运行统一监控脚本
            result = subprocess.run(  # nosec B603 # 受控的监控脚本执行
                [
                    sys.executable,
                    "scripts/unified_db_performance_monitor.py",
                    "--format",
                    "json",
                ],
                capture_output=True,
                text=True,
                timeout=60,
            )

            if result.returncode != 0:
                print(f"❌ 统一监控脚本执行失败: {result.stderr}")
                return {"error": result.stderr}

            # 查找最新的报告文件
            reports_dir = Path("reports")
            if reports_dir.exists():
                json_files = list(reports_dir.glob("unified_performance_*.json"))
                if json_files:
                    latest_file = max(json_files, key=lambda x: x.stat().st_mtime)
                    with open(latest_file, encoding="utf-8") as f:
                        return json.load(f)

            return {"error": "未找到统一监控报告文件"}

        except Exception as e:
            return {"error": str(e)}

    def validate_connection_stats(self, legacy: dict, unified: dict) -> dict[str, Any]:
        """验证连接统计数据"""
        legacy_conn = legacy.get("connection_stats", {})
        unified_conn = unified.get("connection_stats", {})

        # 关键指标对比
        key_metrics = ["total_connections", "active_connections", "idle_connections"]

        validation = {"status": "pass", "details": {}, "errors": []}

        for metric in key_metrics:
            legacy_val = legacy_conn.get(metric, 0)
            unified_val = unified_conn.get(metric, 0)

            # 允许小幅差异（因为查询时间不同）
            diff = abs(legacy_val - unified_val)
            max_allowed_diff = max(3, legacy_val * 0.1)  # 最多3个连接或10%差异

            validation["details"][metric] = {
                "legacy": legacy_val,
                "unified": unified_val,
                "difference": diff,
                "within_threshold": diff <= max_allowed_diff,
            }

            if diff > max_allowed_diff:
                validation["status"] = "fail"
                validation["errors"].append(
                    f"{metric}差异过大: {diff} (允许范围: {max_allowed_diff})"
                )

        return validation

    def validate_database_size(self, legacy: dict, unified: dict) -> dict[str, Any]:
        """验证数据库大小"""
        legacy_size = legacy.get("database_size", {})
        unified_size = unified.get("database_size", {})

        validation = {"status": "pass", "details": {}, "errors": []}

        # 比较字节数（更精确）
        legacy_bytes = legacy_size.get("database_size_bytes", 0)
        unified_bytes = unified_size.get("database_size_bytes", 0)

        # 数据库大小应该完全一致（除非有并发写入）
        diff = abs(legacy_bytes - unified_bytes)
        max_allowed_diff = legacy_bytes * 0.01  # 1%差异

        validation["details"] = {
            "legacy_bytes": legacy_bytes,
            "unified_bytes": unified_bytes,
            "legacy_pretty": legacy_size.get("database_size", "N/A"),
            "unified_pretty": unified_size.get("database_size", "N/A"),
            "difference_bytes": diff,
            "within_threshold": diff <= max_allowed_diff,
        }

        if diff > max_allowed_diff:
            validation["status"] = "fail"
            validation["errors"].append(f"数据库大小差异过大: {diff} bytes")

        return validation

    def validate_table_stats(
        self, legacy: dict, unified: dict, key_tables: list[str] | None = None
    ) -> dict[str, Any]:
        """验证表统计数据"""
        legacy_tables = {t["tablename"]: t for t in legacy.get("table_stats", [])}
        unified_tables = {t["tablename"]: t for t in unified.get("table_stats", [])}

        validation = {
            "status": "pass",
            "details": {},
            "errors": [],
            "table_count_legacy": len(legacy_tables),
            "table_count_unified": len(unified_tables),
        }

        # 检查表数量
        if len(legacy_tables) != len(unified_tables):
            validation["status"] = "warn"
            validation["errors"].append(
                f"表数量不一致: legacy={len(legacy_tables)}, unified={len(unified_tables)}"
            )

        # 检查关键表的数据
        if key_tables is None:
            key_tables = []  # 默认不检查任何表，可传参指定关键表名

        for table_name in key_tables:
            if table_name in legacy_tables and table_name in unified_tables:
                legacy_table = legacy_tables[table_name]
                unified_table = unified_tables[table_name]

                # 比较live_tuples（行数）
                legacy_rows = legacy_table.get("live_tuples", 0)
                unified_rows = unified_table.get("live_tuples", 0)

                diff = abs(legacy_rows - unified_rows)
                max_allowed_diff = max(1, legacy_rows * 0.05)  # 5%差异或至少1行

                validation["details"][table_name] = {
                    "legacy_rows": legacy_rows,
                    "unified_rows": unified_rows,
                    "difference": diff,
                    "within_threshold": diff <= max_allowed_diff,
                }

                if diff > max_allowed_diff:
                    validation["status"] = "fail"
                    validation["errors"].append(f"表{table_name}行数差异过大: {diff}")

        return validation

    def validate_cache_hit_ratio(self, legacy: dict, unified: dict) -> dict[str, Any]:
        """验证缓存命中率"""
        legacy_cache = legacy.get("cache_hit_ratio", {})
        unified_cache = unified.get("cache_hit_ratio", {})

        validation = {"status": "pass", "details": {}, "errors": []}

        legacy_ratio = legacy_cache.get("cache_hit_ratio")
        unified_ratio = unified_cache.get("cache_hit_ratio")

        if legacy_ratio is not None and unified_ratio is not None:
            diff = abs(legacy_ratio - unified_ratio)
            max_allowed_diff = 5.0  # 5%差异

            validation["details"] = {
                "legacy_ratio": legacy_ratio,
                "unified_ratio": unified_ratio,
                "difference": diff,
                "within_threshold": diff <= max_allowed_diff,
            }

            if diff > max_allowed_diff:
                validation["status"] = "fail"
                validation["errors"].append(f"缓存命中率差异过大: {diff:.2f}%")
        else:
            validation["status"] = "warn"
            validation["errors"].append("缓存命中率数据不完整")

        return validation

    async def run_validation(self) -> dict[str, Any]:
        """运行完整验证"""
        print("🚀 开始监控数据验证...")
        print("=" * 60)

        # 获取监控数据
        legacy_data = await self.run_legacy_monitor()
        unified_data = await self.run_unified_monitor()

        if "error" in legacy_data:
            return {
                "status": "error",
                "message": f"原生监控失败: {legacy_data['error']}",
            }

        if "error" in unified_data:
            return {
                "status": "error",
                "message": f"统一监控失败: {unified_data['error']}",
            }

        # 执行各项验证
        # 可根据实际情况指定关键表名列表
        key_tables: list[str] = []  # 例如: ["users", "orders"]

        validation_results = {
            "timestamp": datetime.now().isoformat(),
            "overall_status": "pass",
            "validations": {
                "connection_stats": self.validate_connection_stats(
                    legacy_data, unified_data
                ),
                "database_size": self.validate_database_size(legacy_data, unified_data),
                "table_stats": self.validate_table_stats(
                    legacy_data, unified_data, key_tables=key_tables
                ),
                "cache_hit_ratio": self.validate_cache_hit_ratio(
                    legacy_data, unified_data
                ),
            },
            "raw_data": {"legacy": legacy_data, "unified": unified_data},
        }

        # 计算总体状态
        has_failures = any(
            v["status"] == "fail" for v in validation_results["validations"].values()
        )
        has_warnings = any(
            v["status"] == "warn" for v in validation_results["validations"].values()
        )

        if has_failures:
            validation_results["overall_status"] = "fail"
        elif has_warnings:
            validation_results["overall_status"] = "warn"

        return validation_results

    def print_validation_summary(self, results: dict[str, Any]):
        """打印验证摘要"""
        overall_status = results.get("overall_status", "unknown")

        print("\n📊 监控数据验证结果")
        print("=" * 60)

        # 总体状态
        status_emoji = {"pass": "✅", "warn": "⚠️", "fail": "❌", "error": "💥"}

        emoji = status_emoji.get(overall_status, "❓")
        print(f"{emoji} 总体状态: {overall_status.upper()}")

        # 各项验证详情
        validations = results.get("validations", {})

        for name, validation in validations.items():
            status = validation.get("status", "unknown")
            emoji = status_emoji.get(status, "❓")
            print(f"\n{emoji} {name}:")

            details = validation.get("details", {})
            errors = validation.get("errors", [])

            # 打印关键指标
            if name == "connection_stats":
                for metric, data in details.items():
                    threshold_ok = "✅" if data["within_threshold"] else "❌"
                    print(
                        f"   {metric}: legacy={data['legacy']}, unified={data['unified']}, diff={data['difference']} {threshold_ok}"
                    )

            elif name == "database_size":
                threshold_ok = "✅" if details["within_threshold"] else "❌"
                print(
                    f"   大小: {details['legacy_pretty']} vs {details['unified_pretty']} {threshold_ok}"
                )

            elif name == "table_stats":
                print(
                    f"   表数量: legacy={validation['table_count_legacy']}, unified={validation['table_count_unified']}"
                )
                for table, data in details.items():
                    threshold_ok = "✅" if data["within_threshold"] else "❌"
                    print(
                        f"   {table}: legacy={data['legacy_rows']}, unified={data['unified_rows']} {threshold_ok}"
                    )

            elif name == "cache_hit_ratio" and details:
                threshold_ok = "✅" if details["within_threshold"] else "❌"
                print(
                    f"   命中率: {details['legacy_ratio']:.2f}% vs {details['unified_ratio']:.2f}% {threshold_ok}"
                )

            # 打印错误信息
            for error in errors:
                print(f"   🔍 {error}")

        print("\n" + "=" * 60)

    async def save_validation_report(self, results: dict[str, Any]):
        """保存验证报告"""
        reports_dir = Path("reports")
        reports_dir.mkdir(exist_ok=True)

        report_file = reports_dir / f"monitor_validation_{self.timestamp}.json"

        with open(report_file, "w", encoding="utf-8") as f:
            json.dump(results, f, indent=2, ensure_ascii=False, default=str)

        print(f"📄 验证报告已保存到: {report_file}")
        return report_file


async def main():
    """主函数"""
    validator = MonitorValidation()

    try:
        # 运行验证
        results = await validator.run_validation()

        # 打印摘要
        validator.print_validation_summary(results)

        # 保存报告
        await validator.save_validation_report(results)

        # 根据结果设置退出码
        overall_status = results.get("overall_status", "error")
        if overall_status == "fail" or overall_status == "error":
            print("\n💥 验证失败，请检查上述问题")
            exit(1)
        elif overall_status == "warn":
            print("\n⚠️ 验证完成，但存在警告，请注意")
            exit(0)
        else:
            print("\n✅ 验证通过，统一监控与原生监控数据一致")
            exit(0)

    except Exception as e:
        print(f"❌ 验证过程异常: {e}")
        import traceback

        traceback.print_exc()
        exit(1)


if __name__ == "__main__":
    asyncio.run(main())
